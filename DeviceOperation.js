/**
 * 设备操作模块 - 封装无障碍操作和HID操作
 * 作者: Claude
 * 日期: 2025-07-01
 */

// 检测是否在AutoJS环境中
var isAutoJsEnv = typeof auto !== 'undefined';

// 引入依赖
var hid = require('./hid.js');

// 标记是否已经检查过无障碍服务
var 已检查无障碍服务 = false;

// 操作模式
var 操作模式 = {
    // 无障碍: 1,  // 已禁用 - 使用无障碍服务进行操作
    HID: 2,     // 使用HID设备进行操作
    ROOT: 3     // 使用ROOT权限进行操作
};

// 确保在全局作用域中也能访问操作模式
if (typeof global !== 'undefined') {
    global.操作模式 = 操作模式;
}

// 如果有module.exports，也导出到模块
if (typeof module !== 'undefined' && module.exports) {
    if (!module.exports) module.exports = {};
    module.exports.操作模式 = 操作模式;
}

// 交互操作模式（滑动/点击/长按等）  HID ROOT
var 当前交互模式 = 操作模式.ROOT;
// 窗口信息模式（获取XML/查找元素等）  ROOT
var 当前窗口信息模式 = 操作模式.ROOT;

function 设置交互操作模式(模式) {
    if (模式 === 1) {
        console.error("❌ 无障碍模式已被禁用，自动切换到ROOT模式");
        模式 = 操作模式.ROOT;
    }
    当前交互模式 = 模式;
    console.log("已切换交互操作模式为: " + (模式 === 操作模式.HID ? "HID" : "ROOT"));
}
function 设置窗口信息模式(模式) {
    if (模式 === 1) {
        console.error("❌ 无障碍模式已被禁用，自动切换到ROOT模式");
        模式 = 操作模式.ROOT;
    }
    if (模式 !== 操作模式.ROOT) {
        throw new Error("窗口信息模式只允许设置为ROOT！");
    }
    当前窗口信息模式 = 模式;
    console.log("已切换窗口信息模式为: ROOT");
}

function 获取交互操作模式() {
    return 当前交互模式;
}

function 获取窗口信息模式() {
    return 当前窗口信息模式;
}

// 当前操作模式
var 当前模式 = 操作模式.ROOT;

// 在文件顶部添加RootAutomator支持
var rootAutomator = null;
function getRootAutomator() {
    // 在ROOT模式下，直接返回null，不再尝试初始化RootAutomator
    // 这样会直接使用shell命令执行操作
    console.log("ROOT模式下直接使用shell命令执行操作，不初始化RootAutomator");
    return null;
}

/**
 * ROOT模式专用 - 无障碍服务检查（已禁用）
 * @returns {boolean} 始终返回true（ROOT模式下不需要无障碍服务）
 */
function 确保无障碍服务() {
    console.log("ROOT模式专用，跳过无障碍服务检查");
    return true;
}

/**
 * 设置操作模式
 * @param {number} 模式 - 操作模式（2:HID, 3:ROOT）
 * @returns {boolean} 是否设置成功
 */
function 设置操作模式(模式) {
    if (模式 === 1) {
        console.error("❌ 无障碍模式已被禁用，自动切换到ROOT模式");
        模式 = 操作模式.ROOT;
    }

    if (模式 === 操作模式.HID && !hid_ison()) {
        console.error("无法切换到HID模式：HID设备未连接");
        return false;
    }

    // 设置所有模式变量，保持一致性
    当前模式 = 模式;

    // 设置交互模式
    设置交互操作模式(模式);

    // 设置窗口信息模式（HID模式下窗口信息只能用ROOT）
    if (模式 === 操作模式.HID) {
        设置窗口信息模式(操作模式.ROOT); // HID模式下默认使用ROOT获取窗口信息
    } else {
        设置窗口信息模式(模式);
    }

    // ROOT模式专用，不检查无障碍服务
    if (模式 === 操作模式.ROOT) {
        console.log("ROOT模式专用，完全跳过无障碍服务检查");
    }

    console.log("已切换到" + (模式 === 操作模式.HID ? "HID" : "ROOT") + "模式");
    return true;
}

/**
 * 获取当前操作模式
 * @returns {number} 当前操作模式
 */
function 获取当前模式() {
    return 当前模式;
}

/**
 * 检查HID设备是否连接
 * @returns {boolean} 是否连接
 */
function hid_ison() {
    try {
        if (!isAutoJsEnv) {
            console.log("模拟检查HID设备连接");
            return false;
        }
        return hid.hid_ison();
    } catch (e) {
        console.error("检查HID连接出错:", e);
        return false;
    }
}



/**
 * 读取本地配置文件
 * @param {string} 配置项名称 - 要读取的配置项名称
 * @param {any} 默认值 - 如果配置项不存在时返回的默认值
 * @returns {any} 配置项的值
 */
function 读取本地配置(配置项名称, 默认值 = null) {
    try {
        var CONFIG_FILE = "/sdcard/MIUI/backup/xiaohongshu_config.json";

        if (!files.exists(CONFIG_FILE)) {
            console.log(`配置文件不存在，返回默认值: ${配置项名称} = ${默认值}`);
            return 默认值;
        }

        var 配置内容 = files.read(CONFIG_FILE);
        if (!配置内容) {
            console.log(`配置文件为空，返回默认值: ${配置项名称} = ${默认值}`);
            return 默认值;
        }

        var 配置对象 = JSON.parse(配置内容);

        if (配置对象.hasOwnProperty(配置项名称)) {
            var 配置值 = 配置对象[配置项名称];
            console.log(`读取本地配置: ${配置项名称} = ${配置值}`);
            return 配置值;
        }

        console.log(`配置项 "${配置项名称}" 未找到，返回默认值: ${默认值}`);
        return 默认值;
    } catch (e) {
        console.error(`读取本地配置出错: ${e.message}，返回默认值: ${默认值}`);
        return 默认值;
    }
}

/**
 * 保存到本地配置文件
 * @param {string} 配置项名称 - 要保存的配置项名称
 * @param {any} 配置值 - 要保存的配置值
 * @returns {boolean} 是否保存成功
 */
function 保存到本地配置(配置项名称, 配置值) {
    try {
        var CONFIG_FILE = "/sdcard/MIUI/backup/xiaohongshu_config.json";

        // 读取现有配置
        var 现有配置 = {};
        if (files.exists(CONFIG_FILE)) {
            var 配置内容 = files.read(CONFIG_FILE);
            if (配置内容) {
                try {
                    现有配置 = JSON.parse(配置内容);
                } catch (parseError) {
                    console.error(`解析现有配置文件出错: ${parseError.message}，将创建新配置`);
                    现有配置 = {};
                }
            }
        }

        // 更新指定配置项
        现有配置[配置项名称] = 配置值;

        // 保存配置文件，使用格式化的JSON
        var 新配置内容 = JSON.stringify(现有配置, null, 2);
        files.write(CONFIG_FILE, 新配置内容);

        console.log(`✅ 配置项保存成功: ${配置项名称} = ${JSON.stringify(配置值)}`);
        return true;

    } catch (e) {
        console.error(`❌ 保存配置项失败: ${e.message}`);
        return false;
    }
}

/**
 * 点击坐标
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @param {Object} 选项 - 点击选项
 * @param {boolean} [选项.随机偏移=true] - 是否添加随机偏移
 * @param {number} [选项.偏移范围=5] - 随机偏移范围
 * @returns {boolean} 是否点击成功
 */
function 点击(x, y, 选项 = {}) {
    // 如果坐标为0，直接返回不点击
    if (!x || !y) {
        console.error(`点击坐标无效: (${x}, ${y})，不执行点击`);
        return { 成功: false, 信息: "坐标为0，不执行点击" };
    }

    // 随机偏移
    var 随机偏移 = 选项.随机偏移 !== false;
    var 偏移范围 = 选项.偏移范围 || 5;
    var 点击X = x;
    var 点击Y = y;
    if (随机偏移) {
        点击X += Math.floor(Math.random() * 偏移范围 * 2 - 偏移范围);
        点击Y += Math.floor(Math.random() * 偏移范围 * 2 - 偏移范围);
    }
    // 确保坐标不为负数
    点击X = Math.max(0, 点击X);
    点击Y = Math.max(0, 点击Y);

    // 只保留ROOT和HID两种点击
    switch (当前模式) {
        case 操作模式.ROOT:
            try {
                let cmd = "su -c 'input tap " + 点击X + " " + 点击Y + "'";
                console.log(cmd);
                let result = shell(cmd, true);
                if (result && result.code === 0) {
                    return { 成功: true, 信息: "ROOT模式点击成功" };
                } else {
                    return { 成功: false, 信息: result ? result.error : "ROOT模式点击失败" };
                }
            } catch (e) {
                console.error("shell点击出错:", e);
                return { 成功: false, 信息: "ROOT模式点击异常: " + e.message };
            }
        default:
            console.error("未知的操作模式: " + 当前模式);
            return { 成功: false, 信息: "未知的操作模式: " + 当前模式 };
    }
}


/**
 * 连续点击坐标
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @param {number} 次数 - 点击次数
 * @param {number} 间隔 - 点击间隔(毫秒)
 * @param {Object} 选项 - 点击选项
 * @returns {boolean} 是否点击成功
 */
function 连续点击(x, y, 次数, 间隔, 选项 = {}) {
    // 根据当前模式执行不同的连续点击操作
    switch (当前模式) {
        // 无障碍模式已被禁用

        case 操作模式.HID:
            // 使用HID设备连续点击
            try {
                return hid.hid_clicks(x, y, 次数, 间隔);
            } catch (e) {
                console.error("HID连续点击出错:", e);
                return false;
            }

        case 操作模式.ROOT:
            // 使用ROOT权限连续点击 - 直接使用shell命令
            try {
                for (var i = 0; i < 次数; i++) {
                    let cmd = `su -c "input tap ${x} ${y}"`;
                    let result = shell(cmd, true);
                    console.log(`第${i + 1}次shell点击结果: ${JSON.stringify(result)}`);

                    if (result === undefined) {
                        console.error(`第${i + 1}次shell命令返回undefined，可能是权限问题或命令执行失败`);
                    }

                    if (i < 次数 - 1) sleep(间隔);
                }
                return true;
            } catch (e) {
                console.error("shell连续点击出错:", e);
                return false;
            }

        default:
            console.error("未知的操作模式: " + 当前模式);
            return { 成功: false, 信息: "未知的操作模式: " + 当前模式 };
    }
}

/**
 * 长按坐标
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @param {number} 时长 - 长按时长(毫秒)
 * @param {Object} 选项 - 点击选项
 * @returns {boolean} 是否长按成功
 */
function 长按(x, y, 时长, 选项 = {}) {
    // 确保输入坐标是有效的数字
    if (typeof x !== 'number' || typeof y !== 'number' || isNaN(x) || isNaN(y)) {
        console.error(`无效的坐标值: (${x}, ${y})`);
        return false;
    }

    // 根据当前模式执行不同的长按操作
    switch (当前模式) {
        // 无障碍模式已被禁用

        case 操作模式.HID:
            // 使用HID设备长按
            try {
                // 确保坐标不为负数
                x = Math.max(0, x);
                y = Math.max(0, y);

                // 确保坐标不超出屏幕范围
                if (isAutoJsEnv && typeof device !== 'undefined') {
                    x = Math.min(x, device.width - 1);
                    y = Math.min(y, device.height - 1);
                }

                console.log(`HID长按坐标: (${x}, ${y}), 时长: ${时长}ms`);
                return hid.hid_clicks(x, y, 时长);  // 注意：函数名可能需要调整，应该是长按函数
            } catch (e) {
                console.error("HID长按出错:", e);
                return false;
            }

        case 操作模式.ROOT:
            // 使用ROOT权限长按 - 直接使用shell命令
            try {
                // 使用shell命令模拟长按
                let cmd = `su -c "input swipe ${x} ${y} ${x} ${y} ${时长}"`;
                let result = shell(cmd, true);
                console.log("shell长按结果: " + JSON.stringify(result));
                return result && result.code === 0;
            } catch (e) {
                console.error("shell长按出错:", e);
                return false;
            }

        default:
            console.error("未知的操作模式: " + 当前模式);
            return { 成功: false, 信息: "未知的操作模式: " + 当前模式 };
    }
}

/**
 * 滑动操作
 * @param {number} x1 - 起点X坐标
 * @param {number} y1 - 起点Y坐标
 * @param {number} x2 - 终点X坐标
 * @param {number} y2 - 终点Y坐标
 * @param {Object} 选项 - 滑动选项
 * @returns {boolean} 是否滑动成功
 */
function 滑动(x1, y1, x2, y2, 选项 = {}) {
    try {
        // 记录上次操作
        if (选项 && 选项.上次操作) {
            global.上次操作类型 = 选项.上次操作;
            console.log(`记录上次操作类型: ${global.上次操作类型}`);
        } else {
            global.上次操作类型 = `滑动(${x1},${y1}->${x2},${y2})`;
            console.log(`设置默认上次操作类型: ${global.上次操作类型}`);
        }

        // 执行滑动操作
        if (当前模式 === 操作模式.HID || 当前模式 === 操作模式.HID模式) {
            return HID滑动(x1, y1, x2, y2, 选项);
        } else if (当前模式 === 操作模式.ROOT) {
            return ROOT滑动(x1, y1, x2, y2, 选项);
        } else {
            console.error("未知的操作模式: " + 当前模式 + " (无障碍模式已被禁用)");
            return false;
        }
    } catch (error) {
        console.error(`滑动操作出错: ${error.message}`);
        return false;
    }
}

/**
 * 返回操作
 * @param {Object} 选项 - 返回选项
 * @param {number} [选项.等待时间=1000] - 返回后等待时间(毫秒)
 * @returns {boolean} 是否返回成功
 */
function 返回(选项 = {}) {
    var 等待时间 = 选项.等待时间 || 1000;

    // 根据当前模式执行不同的返回操作
    switch (当前模式) {
        // 无障碍模式已被禁用

        case 操作模式.HID:
            // 使用HID设备返回
            try {
                var 结果 = hid.hid_back();
                sleep(等待时间);
                return 结果;
            } catch (e) {
                console.error("HID返回出错:", e);
                return false;
            }
        case 操作模式.ROOT:
            // 使用ROOT权限返回
            try {
                // 直接用shell命令返回
                let result = shell('input keyevent 4', true);
                sleep(等待时间);
                return result && result.code === 0;
            } catch (e) {
                console.error("ROOT返回出错:", e);
                return false;
            }
        default:
            console.error("未知的操作模式: " + 当前模式);
            return { 成功: false, 信息: "未知的操作模式: " + 当前模式 };
    }
}

/**
 * 主页键操作
 * @param {Object} 选项 - 主页键选项
 * @param {number} [选项.等待时间=1000] - 主页键后等待时间(毫秒)
 * @returns {boolean} 是否操作成功
 */
function 主页键(选项 = {}) {
    var 等待时间 = 选项.等待时间 || 1000;

    // 根据当前模式执行不同的主页键操作
    switch (当前模式) {
        // 无障碍模式已被禁用

        case 操作模式.HID:
            // 使用HID设备主页键
            try {
                var 结果 = hid.hid_home();
                sleep(等待时间);
                return 结果;
            } catch (e) {
                console.error("HID主页键出错:", e);
                return false;
            }

        case 操作模式.ROOT:
            // 使用ROOT权限主页键
            console.log("ROOT主页键");
            try {
                // 使用shell命令模拟主页键
                let cmd = `su -c "input keyevent 3"`;
                let result = shell(cmd, true);
                console.log("shell主页键结果: " + JSON.stringify(result));
                sleep(等待时间);
                return result && result.code === 0;
            } catch (e) {
                console.error("ROOT主页键出错:", e);
                return false;
            }

        default:
            console.error("未知的操作模式: " + 当前模式);
            return { 成功: false, 信息: "未知的操作模式: " + 当前模式 };
    }
}

/**
 * 后台任务键操作
 * @param {Object} 选项 - 后台任务键选项
 * @param {number} [选项.等待时间=1000] - 操作后等待时间(毫秒)
 * @param {boolean} [选项.使用V2=false] - 是否使用V2方法(特殊机型)
 * @returns {boolean} 是否操作成功
 */
function 后台任务键(选项 = {}) {
    var 等待时间 = 选项.等待时间 || 1000;
    var 使用V2 = 选项.使用V2 || false;

    // 根据当前模式执行不同的后台任务键操作
    switch (当前模式) {
        // 无障碍模式已被禁用

        case 操作模式.HID:
            // 使用HID设备后台任务键
            try {
                var 结果;
                if (使用V2) {
                    结果 = hid.hid_recentsV2();
                } else {
                    结果 = hid.hid_recents();
                }
                sleep(等待时间);
                return 结果;
            } catch (e) {
                console.error("HID后台任务键出错:", e);
                return false;
            }

        case 操作模式.ROOT:
            // 使用ROOT权限后台任务键
            try {
                // 使用shell命令模拟后台任务键
                let cmd = `su -c "input keyevent 187"`;
                let result = shell(cmd, true);
                console.log("shell后台任务键结果: " + JSON.stringify(result));
                sleep(等待时间);
                return result && result.code === 0;
            } catch (e) {
                console.error("ROOT后台任务键出错:", e);
                return false;
            }

        default:
            console.error("未知的操作模式: " + 当前模式);
            return { 成功: false, 信息: "未知的操作模式: " + 当前模式 };
    }
}

/**
 * 输入文本
 * @param {string} 文本 - 要输入的文本
 * @param {Object} 选项 - 输入选项
 * @param {boolean} [选项.清除现有文本=false] - 是否先清除现有文本
 * @returns {boolean} 是否输入成功
 */
function 输入文本(文本, 选项 = {}) {
    var 清除现有文本 = 选项.清除现有文本 || false;
    console.log(`尝试输入文本: "${文本}", 清除现有文本: ${清除现有文本}`);

    try {
        // 如果需要清除现有文本
        if (清除现有文本) {
            // 全选并删除现有文本
            try {
                console.log("ROOT模式下清除现有文本");

                // 方法1：先尝试全选删除
                try {
                    // 使用shell命令模拟全选 (Ctrl+A)
                    shell('su -c "input keyevent 29 30"', true);
                    sleep(200);
                    // 删除选中的文本
                    shell('su -c "input keyevent 67"', true);
                    sleep(200);
                } catch (e) {
                    console.log("全选删除失败:", e);
                }

                // 方法2：移动光标到末尾再逐个删除
                try {
                    // 先按Ctrl+End移动到文本末尾
                    shell('su -c "input keyevent 29 123"', true); // Ctrl+End
                    sleep(100);

                    // 确保文本框为空，根据要输入的文本长度来决定删除次数
                    // 取文本长度和30的最大值，确保能删除足够的字符
                    let deleteCount = Math.max(文本.length, 30) + 2;
                    console.log(`执行${deleteCount}次退格删除`);
                    for (let i = 0; i < deleteCount; i++) {
                        shell('su -c "input keyevent 67"', true); // 退格键
                        sleep(30); // 减少延迟提高效率
                    }
                } catch (e) {
                    console.log("移动光标删除失败:", e);
                }
            } catch (e) {
                console.error("ROOT全选删除文本出错:", e);
            }
        }

        // 优先使用直接输入方式
        try {
            console.log("ROOT模式下直接输入文本: " + 文本);
            let escapedText = 文本.replace(/'/g, "\\'").replace(/"/g, '\\"');
            let cmd = `su -c "input text '${escapedText}'"`;
            let result = shell(cmd, true);

            if (result && result.code === 0) {
                console.log("ROOT直接输入文本成功");
                return true;
            } else {
                console.log("ROOT直接输入文本失败，尝试剪贴板方式");
            }
        } catch (e) {
            console.error("ROOT直接输入文本出错:", e);
        }

        // 如果直接输入失败，尝试使用剪贴板方式
        try {
            // 设置剪贴板内容
            if (typeof setClip === 'function') {
                console.log("设置剪贴板内容: " + 文本);
                setClip(文本);
                sleep(300); // 增加延迟确保剪贴板内容设置成功

                // 使用shell命令模拟粘贴 (Ctrl+V)
                console.log("执行ROOT粘贴操作");
                let result = shell('su -c "input keyevent 29 47"', true);
                sleep(200); // 等待粘贴操作完成

                // 检查是否成功
                if (result && result.code === 0) {
                    console.log("ROOT粘贴操作成功");
                    return true;
                }

                // 如果上面的方法失败，尝试使用输入法的粘贴功能
                console.log("尝试备用粘贴方式");

                // 先点击输入区域确保激活
                result = shell('su -c "input tap 500 900"', true); // 点击输入区域唤出键盘
                sleep(300);

                // 长按输入区域
                result = shell('su -c "input swipe 500 900 500 900 800"', true); // 长按输入区域
                sleep(500);

                // 点击粘贴选项
                // 尝试多个可能的粘贴按钮位置
                let positions = [
                    [300, 800], // 通用位置
                    [200, 700], // 低位置
                    [400, 700], // 右侧位置
                    [300, 600]  // 高位置
                ];

                for (let pos of positions) {
                    result = shell(`su -c "input tap ${pos[0]} ${pos[1]}"`, true);
                    sleep(300);
                }

                console.log("备用粘贴方式执行完成");
                return true;
            } else {
                console.log("setClip函数不可用，尝试最后的备用方案");
                // 最后的备用方案：直接使用input text命令
                let escapedText = 文本.replace(/'/g, "\\'").replace(/"/g, '\\"');
                let cmd = `su -c "input text '${escapedText}'"`;
                let result = shell(cmd, true);
                return result && result.code === 0;
            }
        } catch (e) {
            console.error("ROOT输入文本出错:", e);
            // 最后的备用方案：逐字符输入
            try {
                console.log("尝试逐字符输入");
                for (let i = 0; i < 文本.length; i++) {
                    let char = 文本.charAt(i);
                    let escapedChar = char.replace(/'/g, "\\'").replace(/"/g, '\\"');
                    let cmd = `su -c "input text '${escapedChar}'"`;
                    shell(cmd, true);
                    sleep(100);
                }
                return true;
            } catch (err) {
                console.error("ROOT逐字符输入也失败:", err);
                return false;
            }
        }
    } catch (e) {
        console.error("ROOT输入文本出错:", e);
        return false;
    }

}

/**
 * 检查设备电量
 * @returns {number|null} 设备电量(0-100)或null(获取失败)
 */
function 获取电量() {
    // 根据当前模式执行不同的获取电量操作
    switch (当前模式) {
        // 无障碍模式已被禁用

        case 操作模式.HID:
            // 使用HID设备获取电量
            try {
                return hid.hid_getBatteryLevel();
            } catch (e) {
                console.error("HID获取电量出错:", e);
                return null;
            }

        case 操作模式.ROOT:
            // 使用ROOT权限获取电量
            try {
                // 使用shell命令获取电池信息
                let result = shell('su -c "dumpsys battery | grep level"', true);
                if (result && result.code === 0 && result.result) {
                    // 解析电量百分比
                    let match = result.result.match(/level:\s*(\d+)/i);
                    if (match && match[1]) {
                        return parseInt(match[1]);
                    }
                }
                console.error("ROOT获取电量失败");
                return null;
            } catch (e) {
                console.error("ROOT获取电量出错:", e);
                return null;
            }

        default:
            console.error("未知的操作模式: " + 当前模式);
            return null;
    }
}

/**
 * 检查设备充电状态
 * @returns {boolean|null} 是否正在充电或null(获取失败)
 */
function 检查充电状态() {
    // 根据当前模式执行不同的检查充电状态操作
    switch (当前模式) {
        // 无障碍模式已被禁用

        case 操作模式.HID:
            // 使用HID设备检查充电状态
            try {
                return hid.hid_isCharging();
            } catch (e) {
                console.error("HID检查充电状态出错:", e);
                return null;
            }

        case 操作模式.ROOT:
            // 使用ROOT权限检查充电状态
            try {
                // 使用shell命令获取充电状态
                let result = shell('su -c "dumpsys battery | grep powered"', true);
                if (result && result.code === 0 && result.result) {
                    // 检查是否有任何电源连接
                    return result.result.includes("AC powered: true") ||
                        result.result.includes("USB powered: true") ||
                        result.result.includes("Wireless powered: true");
                }
                console.error("ROOT检查充电状态失败");
                return null;
            } catch (e) {
                console.error("ROOT检查充电状态出错:", e);
                return null;
            }

        default:
            console.error("未知的操作模式: " + 当前模式);
            return null;
    }
}

/**
 * 控制设备充电
 * @param {boolean} 开启 - 是否开启充电
 * @returns {boolean} 是否设置成功
 */
function 控制充电(开启) {
    // 根据当前模式执行不同的控制充电操作
    switch (当前模式) {
        case 操作模式.HID:
            // HID模式下控制充电
            try {
                if (开启) {
                    return hid.hid_setPowerOn();
                } else {
                    return hid.hid_setPowerOff();
                }
            } catch (e) {
                console.error("HID控制充电出错:", e);
                return false;
            }

        case 操作模式.ROOT:
            // ROOT模式下控制充电
            // 注意：这需要特定的硬件支持和ROOT权限
            console.log("ROOT模式下控制充电功能需要特定硬件支持");
            return false;

        default:
            console.error("当前模式不支持控制充电功能");
            return false;
    }
}

/**
 * 同步等待指定时间
 * @param {number} 毫秒 - 等待时间(毫秒)
 * @param {Function} 回调 - 等待结束后的回调函数
 */
function sleep(毫秒, 回调) {
    // 在AutoJS环境中使用内置的同步sleep函数
    if (typeof global !== 'undefined' && global.sleep) {
        try {
            global.sleep(毫秒);
            if (typeof 回调 === 'function') {
                回调();
            }
            return;
        } catch (e) {
            console.error("同步sleep失败:", e);
            // 如果失败，回退到使用等待循环
        }
    } else if (typeof java !== 'undefined') {
        try {
            java.lang.Thread.sleep(毫秒);
            if (typeof 回调 === 'function') {
                回调();
            }
            return;
        } catch (e) {
            console.error("同步sleep失败:", e);
            // 如果失败，回退到使用等待循环
        }
    }

    // 使用等待循环实现同步等待
    var start = Date.now();
    while (Date.now() - start < 毫秒) {
        // 循环等待
    }

    // 执行回调
    if (typeof 回调 === 'function') {
        回调();
    }
}

// 无障碍滑动函数已被移除 - 无障碍功能已禁用

/**
 * HID滑动
 * @param {number} x1 - 起点X坐标
 * @param {number} y1 - 起点Y坐标
 * @param {number} x2 - 终点X坐标
 * @param {number} y2 - 终点Y坐标
 * @param {Object} 选项 - 滑动选项
 * @returns {boolean} 是否滑动成功
 */
function HID滑动(x1, y1, x2, y2, 选项 = {}) {
    try {
        // 获取参数
        var 持续时间 = 选项.持续时间 || 500;
        var 步进 = 选项.步进 || 10;
        var 安全边界 = 选项.安全边界 !== false;

        // 获取屏幕尺寸
        var 屏幕宽度 = isAutoJsEnv ? device.width : 1080;
        var 屏幕高度 = isAutoJsEnv ? device.height : 2340;

        // 检查安全边界
        if (安全边界) {
            // 安全边界: 屏幕宽高的5%到95%
            var 安全最小X = 屏幕宽度 * 0.05;
            var 安全最大X = 屏幕宽度 * 0.95;
            var 安全最小Y = 屏幕高度 * 0.05;
            var 安全最大Y = 屏幕高度 * 0.95;

            // 调整起点
            if (x1 < 安全最小X) x1 = 安全最小X;
            if (x1 > 安全最大X) x1 = 安全最大X;
            if (y1 < 安全最小Y) y1 = 安全最小Y;
            if (y1 > 安全最大Y) y1 = 安全最大Y;

            // 调整终点
            if (x2 < 安全最小X) x2 = 安全最小X;
            if (x2 > 安全最大X) x2 = 安全最大X;
            if (y2 < 安全最小Y) y2 = 安全最小Y;
            if (y2 > 安全最大Y) y2 = 安全最大Y;
        }

        if (!isAutoJsEnv) {
            console.log(`模拟HID滑动: (${x1}, ${y1}) -> (${x2}, ${y2}), 持续时间: ${持续时间}ms`);
            return true;
        }

        // 使用HID设备滑动
        try {
            return hid.hid_swipe(x1, y1, x2, y2, 持续时间);
        } catch (e) {
            console.error("HID滑动出错:", e);
            return false;
        }
    } catch (error) {
        console.error(`HID滑动出错: ${error.message}`);
        return false;
    }
}

/**
 * ROOT滑动
 * @param {number} x1 - 起点X坐标
 * @param {number} y1 - 起点Y坐标
 * @param {number} x2 - 终点X坐标
 * @param {number} y2 - 终点Y坐标
 * @param {Object} 选项 - 滑动选项
 * @returns {boolean} 是否滑动成功
 */
function ROOT滑动(x1, y1, x2, y2, 选项 = {}) {
    try {
        // 获取参数
        var 持续时间 = 选项.持续时间 || 500;
        var 步进 = 选项.步进 || 10;
        var 安全边界 = 选项.安全边界 !== false;

        // 获取屏幕尺寸
        var 屏幕宽度 = isAutoJsEnv ? device.width : 1080;
        var 屏幕高度 = isAutoJsEnv ? device.height : 2340;

        // 检查安全边界
        if (安全边界) {
            // 安全边界: 屏幕宽高的5%到95%
            var 安全最小X = 屏幕宽度 * 0.05;
            var 安全最大X = 屏幕宽度 * 0.95;
            var 安全最小Y = 屏幕高度 * 0.05;
            var 安全最大Y = 屏幕高度 * 0.95;

            // 调整起点
            if (x1 < 安全最小X) x1 = 安全最小X;
            if (x1 > 安全最大X) x1 = 安全最大X;
            if (y1 < 安全最小Y) y1 = 安全最小Y;
            if (y1 > 安全最大Y) y1 = 安全最大Y;

            // 调整终点
            if (x2 < 安全最小X) x2 = 安全最小X;
            if (x2 > 安全最大X) x2 = 安全最大X;
            if (y2 < 安全最小Y) y2 = 安全最小Y;
            if (y2 > 安全最大Y) y2 = 安全最大Y;
        }

        if (!isAutoJsEnv) {
            console.log(`模拟ROOT滑动: (${x1}, ${y1}) -> (${x2}, ${y2}), 持续时间: ${持续时间}ms`);
            return true;
        }

        // 使用ROOT设备滑动 - 直接使用shell命令
        try {
            // 使用shell命令模拟滑动
            let cmd = `su -c "input swipe ${Math.round(x1)} ${Math.round(y1)} ${Math.round(x2)} ${Math.round(y2)} ${持续时间}"`;
            let result = shell(cmd, true);
            console.log("shell滑动结果: " + JSON.stringify(result));
            return result && result.code === 0;
        } catch (e) {
            console.error("shell滑动出错:", e);
            return false;
        }
    } catch (error) {
        console.error(`ROOT滑动出错: ${error.message}`);
        return false;
    }
}

/**
 * 通过shell命令获取窗口XML内容
 * 这种方法在ROOT和无障碍模式下都可以使用
 * 
 * @returns {string|null} XML字符串，失败返回null
 */
function 通过shell获取窗口XML() {
    console.log("开始获取界面 XML...");
    for (let i = 0; i < 10; i++) {
        try {
            // 使用su -c命令直接执行，这种方式在MIUI系统上更可靠
            let cmd = `su -c "uiautomator dump --compressed /sdcard/window_dump.xml"`;
            let result = shell(cmd, true);  // 执行命令，true 表示使用 Root 权限
            // 读取 XML 文件内容
            let xmlPath = "/sdcard/window_dump.xml";
            let xmlContent = files.read(xmlPath);
            //console.log(xmlContent);
            if (xmlContent && xmlContent.length > 100) {
                console.log("成功获取 XML 文件，大小: " + xmlContent.length + " 字节");
                return xmlContent;
            } else {
                console.error("XML内容为空或过短");
                //return null;
                sleep(1000);
            }
        } catch (e) {
            console.error("获取界面XML出错: " + e.message);
            //return null;
            sleep(1000);
        }
    }
    return null;
}

/**
 * 使用shell命令执行滑动操作
 * 通过input命令模拟触摸滑动，不依赖无障碍服务
 *
 * @param {number} startX - 起始X坐标
 * @param {number} startY - 起始Y坐标
 * @param {number} endX - 结束X坐标
 * @param {number} endY - 结束Y坐标
 * @param {number} duration - 滑动持续时间(毫秒)
 * @returns {boolean} - 滑动是否成功
 */
function shell滑动(startX, startY, endX, endY, duration) {
    duration = duration || 800;

    try {
        // 构建shell命令
        let cmd = `input swipe ${Math.round(startX)} ${Math.round(startY)} ${Math.round(endX)} ${Math.round(endY)} ${duration}`;
        console.log(`[滑动] 执行: (${Math.round(startX)},${Math.round(startY)}) -> (${Math.round(endX)},${Math.round(endY)}) 时长:${duration}ms`);

        // 执行shell命令
        let result = shell(cmd, true);

        if (result && result.code === 0) {
            return true;
        } else {
            console.error(`[滑动] 失败, 返回码: ${result ? result.code : "null"}`);
            return false;
        }

    } catch (e) {
        console.error(`[滑动] 出错: ${e.message}`);
        return false;
    }
}

/**
 * 刷视频式的贝塞尔曲线向上滑动
 * 模拟用户刷视频时的自然滑动动作
 *
 * @returns {Object} - 滑动结果 {成功: boolean, 信息: string}
 */
function 刷视频滑动() {
    try {
        console.log("[阅读滑动] 开始执行刷视频式滑动");

        // 获取屏幕尺寸
        let screenWidth = device.width;
        let screenHeight = device.height;

        console.log(`[阅读滑动] 屏幕尺寸: ${screenWidth} x ${screenHeight}`);

        // 屏幕中心点
        let centerX = screenWidth * 0.5;
        let centerY = screenHeight * 0.5;

        // 随机生成起点（屏幕中下部，中心左右100像素范围内）
        let startX = centerX + (Math.random() - 0.5) * 200; // 中心左右100像素随机
        let startY = centerY + screenHeight * (0.15 + Math.random() * 0.1); // 屏幕中心下方15%-25%随机

        // 随机生成终点（屏幕中上部，中心左右100像素范围内）
        let endX = centerX + (Math.random() - 0.5) * 200; // 中心左右100像素随机
        let endY = centerY - screenHeight * (0.15 + Math.random() * 0.1); // 屏幕中心上方15%-25%随机

        // 刷视频的滑动时长更短更快 (300-600ms)
        let duration = 300 + Math.random() * 300;

        console.log(`[阅读滑动] 滑动路径: (${Math.round(startX)},${Math.round(startY)}) -> (${Math.round(endX)},${Math.round(endY)})`);
        console.log(`[阅读滑动] 滑动时长: ${Math.round(duration)}ms`);

        // 计算贝塞尔曲线控制点（轻微偏移，创建自然曲线）
        let controlX1 = startX + (endX - startX) * 0.3 + (Math.random() - 0.5) * 50;
        let controlY1 = startY + (endY - startY) * 0.3 + (Math.random() - 0.5) * 50;
        let controlX2 = startX + (endX - startX) * 0.7 + (Math.random() - 0.5) * 50;
        let controlY2 = startY + (endY - startY) * 0.7 + (Math.random() - 0.5) * 50;

        // 计算中间点（贝塞尔曲线的中点，t=0.5）
        let t = 0.5;
        let midX = Math.pow(1 - t, 3) * startX +
            3 * Math.pow(1 - t, 2) * t * controlX1 +
            3 * (1 - t) * Math.pow(t, 2) * controlX2 +
            Math.pow(t, 3) * endX;

        let midY = Math.pow(1 - t, 3) * startY +
            3 * Math.pow(1 - t, 2) * t * controlY1 +
            3 * (1 - t) * Math.pow(t, 2) * controlY2 +
            Math.pow(t, 3) * endY;

        console.log(`[阅读滑动] 贝塞尔中间点: (${Math.round(midX)},${Math.round(midY)})`);

        // 执行滑动
        let success = shell滑动(Math.round(startX), Math.round(startY), Math.round(endX), Math.round(endY), Math.round(duration));

        if (success) {
            console.log("[阅读滑动] ✅ 刷视频滑动完成");
            return { 成功: true, 信息: `滑动完成，时长${Math.round(duration)}ms` };
        } else {
            console.log("[阅读滑动] ❌ 刷视频滑动失败");
            return { 成功: false, 信息: "滑动操作失败" };
        }

    } catch (e) {
        console.error(`[阅读滑动] ❌ 出错: ${e.message}`);
        return { 成功: false, 信息: `滑动出错: ${e.message}` };
    }
}

/**
 * 获取当前窗口的XML信息
 * @returns {string|null} XML字符串，失败返回null
 */
function 获取窗口XML() {
    try {
        var 模式 = 获取窗口信息模式();
        if (模式 === 操作模式.ROOT) {
            // ROOT方式使用shell命令获取XML
            console.log("使用ROOT方式shell命令获取XML");
            let xml内容 = 通过shell获取窗口XML();
            if (xml内容) {
                return xml内容;
            } else {
                console.log("ROOT方式获取XML失败，返回null");
                return null;
            }
        } else if (模式 === 1) {
            // 无障碍模式已被禁用
            console.error("❌ 无障碍模式已被禁用，无法获取窗口XML");
            return null;
        } else {
            // 理论上不会走到这里，因为设置窗口信息模式时已校验
            throw new Error("窗口信息模式只允许无障碍或ROOT");
        }
    } catch (e) {
        console.error("获取窗口XML出错:", e);
        return null;
    }
}

/**
 * 获取元素属性（如bounds、text、desc、clickable等）
 * @param {object} 元素 - 元素对象
 * @param {string} 属性名 - 属性名
 * @returns {*} 属性值，失败返回null
 */
function 获取元素属性(元素, 属性名) {
    if (!元素 || !属性名) return null;
    try {
        var 模式 = 获取窗口信息模式();
        if (模式 === 操作模式.ROOT) {
            // ROOT模式下，元素是从XML解析出来的对象
            // 根据属性名映射到XML属性
            switch (属性名) {
                case 'bounds':
                    return 元素.boundsObj || 元素.bounds();
                case 'text':
                    return 元素.text || 元素["text"];
                case 'desc':
                case 'content-desc':
                    return 元素["content-desc"];
                case 'resourceId':
                case 'id':
                    return 元素["resource-id"];
                case 'className':
                case 'class':
                    return 元素["class"];
                case 'package':
                case 'packageName':
                    return 元素["package"];
                case 'clickable':
                    return 元素["clickable"] === "true";
                case 'enabled':
                    return 元素["enabled"] === "true";
                case 'checked':
                    return 元素["checked"] === "true";
                case 'selected':
                    return 元素["selected"] === "true";
                case 'focused':
                    return 元素["focused"] === "true";
                case 'scrollable':
                    return 元素["scrollable"] === "true";
                case 'longClickable':
                    return 元素["long-clickable"] === "true";
                default:
                    // 如果是直接属性名，尝试直接获取
                    if (元素[属性名] !== undefined) {
                        return 元素[属性名];
                    }

                    // 尝试使用函数方式获取
                    if (typeof 元素[属性名] === 'function') {
                        return 元素[属性名]();
                    }

                    return null;
            }
        } else if (模式 === 操作模式.无障碍) {
            if (typeof 元素[属性名] === 'function') {
                return 元素[属性名]();
            } else if (元素[属性名] !== undefined) {
                return 元素[属性名];
            }
            return null;
        } else {
            throw new Error("窗口信息模式只允许无障碍或ROOT");
        }
    } catch (e) {
        console.error("获取元素属性出错:", e);
        return null;
    }
}

function 获取元素bounds(元素) { return 获取元素属性(元素, 'bounds'); }
function 获取元素text(元素) { return 获取元素属性(元素, 'text'); }
function 获取元素desc(元素) { return 获取元素属性(元素, 'desc'); }
function 获取元素clickable(元素) { return 获取元素属性(元素, 'clickable'); }
function 获取元素enabled(元素) { return 获取元素属性(元素, 'enabled'); }
function 获取元素checked(元素) { return 获取元素属性(元素, 'checked'); }
function 获取元素selected(元素) { return 获取元素属性(元素, 'selected'); }
function 获取元素focused(元素) { return 获取元素属性(元素, 'focused'); }
function 获取元素scrollable(元素) { return 获取元素属性(元素, 'scrollable'); }
function 获取元素longClickable(元素) { return 获取元素属性(元素, 'longClickable'); }
function 获取元素packageName(元素) { return 获取元素属性(元素, 'packageName'); }
function 获取元素resourceId(元素) { return 获取元素属性(元素, 'resourceId'); }
function 获取元素className(元素) { return 获取元素属性(元素, 'className'); }

/**
 * 通过解析XML字符串查找元素
 * @param {string} xmlStr - XML字符串
 * @param {object} 条件 - 查找条件
 * @param {boolean} 全部 - 是否查找全部匹配元素
 * @returns {object|array|null} - 找到的元素或元素数组
 */
function 从XML中查找元素(xmlStr, 条件, 全部) {
    if (!xmlStr) return null;

    try {
        // 提取所有节点数据
        let 所有节点 = [];
        let nodeRegex = /<node[^>]*?\s+([^>]*?)\/>/g;
        let attrRegex = /(\w+)="([^"]*)"/g;

        let match;
        while ((match = nodeRegex.exec(xmlStr)) !== null) {
            let nodeAttrs = match[1];
            let node = {};

            // 提取属性
            let attrMatch;
            while ((attrMatch = attrRegex.exec(nodeAttrs)) !== null) {
                let attrName = attrMatch[1];
                let attrValue = attrMatch[2];
                node[attrName] = attrValue;

                // 特殊处理bounds属性，解析为对象
                if (attrName === "bounds") {
                    // 格式: [left,top][right,bottom]
                    let boundsMatch = attrValue.match(/\[(\d+),(\d+)\]\[(\d+),(\d+)\]/);
                    if (boundsMatch) {
                        node.boundsObj = {
                            left: parseInt(boundsMatch[1]),
                            top: parseInt(boundsMatch[2]),
                            right: parseInt(boundsMatch[3]),
                            bottom: parseInt(boundsMatch[4])
                        };
                    }
                }
            }

            所有节点.push(node);
        }

        // 根据条件过滤节点
        let 匹配节点 = 所有节点.filter(node => {
            let 匹配 = true;

            // 检查每个条件
            for (let 键 in 条件) {
                let 值 = 条件[键];

                if (键 === "className") {
                    // 检查class属性
                    if (node["class"] !== 值) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "text") {
                    // 精确匹配文本
                    if (node["text"] !== 值) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "textContains") {
                    // 包含文本
                    if (!node["text"] || !node["text"].includes(值)) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "textStartsWith") {
                    // 文本开头
                    if (!node["text"] || !node["text"].startsWith(值)) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "textEndsWith") {
                    // 文本结尾
                    if (!node["text"] || !node["text"].endsWith(值)) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "textMatches") {
                    // 文本正则匹配
                    if (!node["text"] || !new RegExp(值).test(node["text"])) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "desc" || 键 === "description") {
                    // 精确匹配描述
                    let desc值 = 键 === "desc" ? 值 : 值;
                    if (node["content-desc"] !== desc值) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "descContains") {
                    // 包含描述
                    if (!node["content-desc"] || !node["content-desc"].includes(值)) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "descMatches") {
                    // 描述正则匹配
                    if (!node["content-desc"] || !new RegExp(值).test(node["content-desc"])) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "id" || 键 === "resourceId") {
                    // 精确匹配ID
                    if (node["resource-id"] !== 值) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "idContains") {
                    // ID包含
                    if (!node["resource-id"] || !node["resource-id"].includes(值)) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "clickable") {
                    // 可点击性
                    if ((node["clickable"] === "true") !== 值) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "enabled") {
                    // 启用状态
                    if ((node["enabled"] === "true") !== 值) {
                        匹配 = false;
                        break;
                    }
                } else if (键 === "boundsInside") {
                    // 边界在指定范围内
                    if (!node.boundsObj) {
                        匹配 = false;
                        break;
                    }

                    let bounds = node.boundsObj;
                    let targetBounds = 值;

                    if (bounds.left < targetBounds.left ||
                        bounds.top < targetBounds.top ||
                        bounds.right > targetBounds.right ||
                        bounds.bottom > targetBounds.bottom) {
                        匹配 = false;
                        break;
                    }
                }
            }

            return 匹配;
        });

        // 添加特殊方法，使API与无障碍方式一致
        匹配节点.forEach(node => {
            // 添加bounds方法
            node.bounds = function () {
                return node.boundsObj;
            };

            // 添加text方法
            node.text = function () {
                return node.text;
            };

            // 添加desc方法
            node.desc = function () {
                return node["content-desc"];
            };

            // 添加id方法
            node.id = function () {
                return node["resource-id"];
            };

            // 添加其他必要方法...
        });

        if (匹配节点.length === 0) {
            return null;
        }

        return 全部 ? 匹配节点 : 匹配节点[0];
    } catch (e) {
        console.error("从XML中查找元素出错:", e);
        return null;
    }
}

/**
 * 通过ROOT方式查找元素
 * 基于XML解析实现
 * @param {object} 条件 - 查找条件
 * @param {boolean} 全部 - 是否查找全部
 * @returns {object|array|null} - 元素或元素数组
 */
function 通过ROOT查找元素(条件, 全部) {
    try {
        // 获取XML
        let xml = 获取窗口XML();
        if (!xml) {
            console.log("获取窗口XML失败");
            return null;
        }

        // 解析XML查找元素
        return 从XML中查找元素(xml, 条件, 全部);
    } catch (e) {
        console.error("通过ROOT查找元素出错:", e);
        return null;
    }
}

/**
 * 通用元素查找（单个/全部）
 * @param {object} 条件 - {className, text, desc, id, ...}
 * @param {boolean} [全部=false] - 是否查找所有
 * @param {number} [超时=1000] - 查找超时时间(毫秒)
 * @returns {object|array|null} 元素或元素数组
 */
function 查找元素(条件 = {}, 全部 = false, 超时 = 1000) {
    try {
        // 确保超时是有效值
        超时 = 超时 || 1000;

        var 模式 = 获取窗口信息模式();
        if (模式 === 操作模式.ROOT) {
            // 使用ROOT方式查找元素
            console.log("使用ROOT方式查找元素");
            return 通过ROOT查找元素(条件, 全部);
        } else if (模式 === 1) {
            console.error("❌ 无障碍模式已被禁用");
            return null;
        } else {
            throw new Error("窗口信息模式只允许ROOT");
        }
    } catch (e) {
        console.error("查找元素出错:", e);
        console.log("错误堆栈:", e.stack);
        return null;
    }
}

/**
 * 查找所有匹配条件的元素
 * @param {object} 条件 - 查找条件
 * @param {number} [超时=1000] - 查找超时时间(毫秒)
 * @returns {array|null} 元素数组
 */
function 查找所有元素(条件 = {}, 超时 = 1000) {
    return 查找元素(条件, true, 超时);
}

/**
 * 通过无障碍服务查找元素 (已禁用)
 * @param {object} 条件 - {className, text, desc, id, ...}
 * @param {boolean} [全部=false] - 是否查找所有
 * @param {number} [超时=1000] - 查找超时时间(毫秒)
 * @returns {object|array|null} 元素或元素数组
 */
function 通过无障碍查找元素(条件 = {}, 全部 = false, 超时 = 1000) {
    console.error("❌ 无障碍查找元素功能已被禁用");
    return null;
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境或AutoJS环境使用require
    module.exports = {
        操作模式,
        设置操作模式,
        获取当前模式,
        设置交互操作模式,
        获取交互操作模式,
        设置窗口信息模式,
        获取窗口信息模式,
        通过shell获取窗口XML,
        通过无障碍查找元素,
        通过ROOT查找元素,
        从XML中查找元素,
        解析XML互动元素,
        hid_ison,
        点击,
        连续点击,
        长按,
        滑动,
        返回,
        主页键,
        后台任务键,
        输入文本,
        获取电量,
        检查充电状态,
        控制充电,
        确保无障碍服务,
        获取窗口XML,
        获取元素属性,
        获取元素bounds,
        获取元素text,
        获取元素desc,
        获取元素clickable,
        获取元素enabled,
        获取元素checked,
        获取元素selected,
        获取元素focused,
        获取元素scrollable,
        获取元素longClickable,
        获取元素packageName,
        获取元素resourceId,
        获取元素className,
        查找元素,
        查找所有元素,
        sleep,
        检查截图权限,
        检查是否进入小红书,
        初始化Root权限,
        执行Root命令
    };
} else {
    // 旧版AutoJS环境，使用全局变量
    // 直接将所有函数和变量添加到全局作用域
    global.DeviceOperation = {
        操作模式,
        设置操作模式,
        获取当前模式,
        设置交互操作模式,
        获取交互操作模式,
        设置窗口信息模式,
        获取窗口信息模式,
        通过shell获取窗口XML,
        通过无障碍查找元素,
        通过ROOT查找元素,
        从XML中查找元素,
        解析XML互动元素,
        hid_ison,
        点击,
        连续点击,
        长按,
        滑动,
        返回,
        主页键,
        后台任务键,
        输入文本,
        获取电量,
        检查充电状态,
        控制充电,
        确保无障碍服务,
        获取窗口XML,
        获取元素属性,
        获取元素bounds,
        获取元素text,
        获取元素desc,
        获取元素clickable,
        获取元素enabled,
        获取元素checked,
        获取元素selected,
        获取元素focused,
        获取元素scrollable,
        获取元素longClickable,
        获取元素packageName,
        获取元素resourceId,
        获取元素className,
        查找元素,
        查找所有元素,
        sleep,
        检查截图权限,
        检查是否进入小红书,
        初始化Root权限,
        执行Root命令,

        // 配置管理功能
        读取本地配置,
        保存到本地配置
    };

    // 确保操作模式也直接添加到全局作用域
    if (typeof global !== 'undefined' && !global.操作模式) {
        global.操作模式 = 操作模式;
    }
}

/**
 * 从XML中解析出互动元素信息
 * 根据111111.md中的示例解析XML中的点赞、收藏、评论数据
 * 
 * @param {string} xmlStr - XML字符串
 * @returns {Object} 互动元素信息
 */
function 解析XML互动元素(xmlStr) {
    if (!xmlStr) return null;

    try {
        // 获取屏幕尺寸
        let 屏幕宽度 = isAutoJsEnv && typeof device !== 'undefined' ? device.width : 1080;
        let 屏幕高度 = isAutoJsEnv && typeof device !== 'undefined' ? device.height : 1920;

        let 结果 = {
            点赞: {
                数量: 0,
                已点赞: false,
                坐标: { x: 0, y: 0 }
            },
            收藏: {
                数量: 0,
                已收藏: false,
                坐标: { x: 0, y: 0 }
            },
            评论: {
                数量: 0,
                坐标: { x: 0, y: 0 }
            },
            文本内容: [],
            内容类型: "未知",
            用户昵称: "",
            标题: "",
            内容: ""
        };

        // 提取所有文本元素
        let 文本元素正则 = /<node[^>]*?text="([^"]*?)"[^>]*?bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*?\/>/g;
        let 文本匹配;
        let 有效文本元素 = [];

        while ((文本匹配 = 文本元素正则.exec(xmlStr)) !== null) {
            let 文本 = 文本匹配[1].trim();
            if (文本 && 文本.length > 0) {
                let x = Math.floor((parseInt(文本匹配[2]) + parseInt(文本匹配[4])) / 2);
                let y = Math.floor((parseInt(文本匹配[3]) + parseInt(文本匹配[5])) / 2);
                有效文本元素.push({ 文本, x, y });
            }
        }

        结果.文本内容 = 有效文本元素;
        console.log(`共找到 ${有效文本元素.length} 个有效文本元素`);

        // 提取用户昵称 - 从content-desc中查找
        let 用户昵称正则 = /content-desc="作者([^"]*?)"/g;
        let 用户昵称匹配;
        while ((用户昵称匹配 = 用户昵称正则.exec(xmlStr)) !== null) {
            let 昵称 = 用户昵称匹配[1].trim();
            // 处理逗号分隔的情况
            if (昵称.includes(",")) {
                昵称 = 昵称.split(",")[1].trim();
            }
            if (昵称 && 昵称.length > 0) {
                结果.用户昵称 = 昵称;
                console.log(`提取到用户昵称: ${结果.用户昵称}`);
                break;
            }
        }

        // 提取标题和内容 - 根据XML结构分析
        let 标题候选 = [];
        let 内容候选 = [];

        for (let 元素 of 有效文本元素) {
            let 文本 = 元素.文本;
            let y = 元素.y;

            // 过滤掉一些不需要的文本
            if (文本.includes("图片,第") ||
                文本.includes("双指左划") ||
                文本.includes("关注") ||
                文本.includes("分享") ||
                文本.includes("说点什么") ||
                文本.includes("宁波市") ||
                文本.includes("已播放到") ||
                文本.includes("暂停")) {
                continue;
            }

            // 标题通常在屏幕上半部分，长度适中，不包含特殊字符
            if (y < 屏幕高度 * 0.5 && 文本.length > 3 && 文本.length < 100 &&
                !文本.includes("&#") && !文本.includes("#")) {
                标题候选.push({ 文本, y });
            }

            // 内容通常在屏幕下半部分，长度较长，可能包含话题标签
            if (y > 屏幕高度 * 0.4 && 文本.length > 10) {
                内容候选.push({ 文本, y });
            }
        }

        // 选择最可能的标题（y坐标最小的）
        if (标题候选.length > 0) {
            标题候选.sort((a, b) => a.y - b.y);
            结果.标题 = 标题候选[0].文本;
            console.log(`提取到标题: ${结果.标题}`);
        }

        // 选择最可能的内容（y坐标最大的长文本）
        if (内容候选.length > 0) {
            内容候选.sort((a, b) => b.y - a.y);
            结果.内容 = 内容候选[0].文本;
            console.log(`提取到内容: ${结果.内容.substring(0, 100)}...`);
        }

        // 提取互动元素
        let 互动元素正则 = /<node[^>]*?content-desc="([^"]*?)"[^>]*?bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*?\/>/g;
        let 互动匹配;

        while ((互动匹配 = 互动元素正则.exec(xmlStr)) !== null) {
            let desc = 互动匹配[1];
            let x = Math.floor((parseInt(互动匹配[2]) + parseInt(互动匹配[4])) / 2);
            let y = Math.floor((parseInt(互动匹配[3]) + parseInt(互动匹配[5])) / 2);

            // 检查是否是点赞元素
            if (desc.includes("点赞")) {
                console.log(`找到content-desc元素: ${desc}, 坐标: (${x}, ${y})`);

                // 改进的点赞数提取逻辑
                let 数量匹配 = desc.match(/(\d+)/);
                if (数量匹配) {
                    结果.点赞.数量 = parseInt(数量匹配[1]);
                } else if (desc.includes("已点赞")) {
                    // 如果显示"已点赞"但没有数字，说明数量至少为1
                    结果.点赞.数量 = 1;
                } else {
                    // 如果只显示"点赞"没有数字，数量为0
                    结果.点赞.数量 = 0;
                }

                // 改进的点赞状态检测
                结果.点赞.已点赞 = desc.includes("已点赞");

                // 记录坐标
                结果.点赞.坐标 = { x, y };

                console.log(`从content-desc中提取点赞信息: 数量=${结果.点赞.数量}, 已点赞=${结果.点赞.已点赞}`);
            }
            // 检查是否是收藏元素
            else if (desc.includes("收藏")) {
                console.log(`找到content-desc元素: ${desc}, 坐标: (${x}, ${y})`);

                // 改进的收藏数提取逻辑
                let 数量匹配 = desc.match(/(\d+)/);
                if (数量匹配) {
                    结果.收藏.数量 = parseInt(数量匹配[1]);
                } else if (desc.includes("已收藏")) {
                    // 如果显示"已收藏"但没有数字，说明数量至少为1
                    结果.收藏.数量 = 1;
                } else {
                    // 如果只显示"收藏"没有数字，数量为0
                    结果.收藏.数量 = 0;
                }

                // 改进的收藏状态检测
                结果.收藏.已收藏 = desc.includes("已收藏");

                // 记录坐标
                结果.收藏.坐标 = { x, y };

                console.log(`从content-desc中提取收藏信息: 数量=${结果.收藏.数量}, 已收藏=${结果.收藏.已收藏}`);
            }
            // 检查是否是评论元素
            else if (desc.includes("评论")) {
                console.log(`找到content-desc元素: ${desc}, 坐标: (${x}, ${y})`);

                // 提取评论数
                let 数量匹配 = desc.match(/\d+/);
                if (数量匹配) {
                    结果.评论.数量 = parseInt(数量匹配[0]);
                }

                // 记录坐标
                结果.评论.坐标 = { x, y };

                console.log(`从content-desc中提取评论信息: ${结果.评论.数量}`);
            }
        }

        // 判断是否为视频页面
        if (xmlStr.includes("content-desc=\"视频") ||
            xmlStr.includes("视频播放器") ||
            xmlStr.includes("播放进度") ||
            xmlStr.includes("暂停")) {
            结果.内容类型 = "视频";
        } else {
            结果.内容类型 = "图文";
        }

        // 新增：处理首页列表中的信息（兼容性检测）
        if (!结果.点赞.坐标 && !结果.收藏.坐标) {
            console.log("🔍 未找到标准按钮，尝试首页列表兼容性检测...");

            // 查找首页列表中的信息格式：如"笔记  东莞...来自深二代的梦想生活 71赞"
            let 首页信息正则 = /<node[^>]*?content-desc="([^"]*?(\d+)赞[^"]*?)"[^>]*?bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*?\/>/g;
            let 首页匹配;

            while ((首页匹配 = 首页信息正则.exec(xmlStr)) !== null) {
                let desc = 首页匹配[1];
                let 赞数 = parseInt(首页匹配[2]);
                let x = Math.floor((parseInt(首页匹配[3]) + parseInt(首页匹配[5])) / 2);
                let y = Math.floor((parseInt(首页匹配[4]) + parseInt(首页匹配[6])) / 2);

                console.log(`找到首页列表信息: ${desc}, 赞数: ${赞数}, 坐标: (${x}, ${y})`);

                // 设置点赞信息（首页列表无法判断是否已点赞，默认未点赞）
                结果.点赞.数量 = 赞数;
                结果.点赞.已点赞 = false;
                结果.点赞.坐标 = { x, y };

                // 判断内容类型
                if (desc.includes("视频")) {
                    结果.内容类型 = "视频";
                } else if (desc.includes("笔记")) {
                    结果.内容类型 = "图文";
                }

                console.log(`从首页列表提取信息: 数量=${结果.点赞.数量}, 类型=${结果.内容类型}`);
                break; // 只处理第一个匹配的信息
            }
        }

        // 输出最终结果
        console.log(`页面类型: ${结果.内容类型}`);
        console.log(`用户昵称: ${结果.用户昵称}`);
        console.log(`标题: ${结果.标题}`);
        console.log(`内容: ${结果.内容.substring(0, 50)}...`);

        // 输出互动元素信息
        if (结果.点赞.坐标) {
            console.log(`✅ 点赞信息: ${结果.点赞.已点赞 ? '已点赞' : '未点赞'}, 数量=${结果.点赞.数量}`);
        } else {
            console.log(`❌ 未找到点赞按钮信息`);
        }

        if (结果.收藏.坐标) {
            console.log(`✅ 收藏信息: ${结果.收藏.已收藏 ? '已收藏' : '未收藏'}, 数量=${结果.收藏.数量}`);
        } else {
            console.log(`❌ 未找到收藏按钮信息`);
        }

        return 结果;
    } catch (e) {
        console.error("解析XML互动元素出错:", e);
        return null;
    }
}


/**
 * 检查是否已进入小红书
 * @returns {boolean} 是否在小红书中
 */
function 检查是否进入小红书() {
    // 根据当前操作模式选择不同的实现方式
    let 当前交互模式 = 获取交互操作模式();
    console.log("检查是否进入小红书");
    if (当前交互模式 === 操作模式.ROOT) {
        // ROOT模式：使用shell命令获取当前应用包名
        console.log("ROOT模式下检查是否进入小红书");
        let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
        if (result.code === 0) {
            let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
            if (match && match[1]) {
                let 当前包名 = match[1];
                console.log("当前应用包名: " + 当前包名);
                return 当前包名 === "com.xingin.xhs";
            }
        }
        console.log("ROOT模式无法确认是否已进入小红书");
        return false;
    } else if (当前交互模式 === 操作模式.无障碍) {
        // 无障碍模式：使用currentPackage获取当前应用包名
        console.log("无障碍模式下检查是否进入小红书");
        if (typeof currentPackage === "function") {
            let 当前包名 = currentPackage();
            console.log("当前应用包名: " + 当前包名);
            return 当前包名 === "com.xingin.xhs";
        } else {
            console.log("无障碍模式下currentPackage函数不可用");
            return false;
        }
    } else {
        // 未知模式
        console.log("未知操作模式，无法检查是否进入小红书");
        return false;
    }
}

// 全局变量，保存root会话状态
let root会话 = false;

/**
 * 初始化Root权限，获取一个root会话
 * @returns {boolean} 是否成功获取root权限
 */
function 初始化Root权限() {
    // 如果已经获取过root权限，直接返回true，不再重复申请
    if (root会话) {
        console.log("已有root会话，无需重新申请权限");
        return true;
    }

    console.log("首次尝试获取root权限...");

    try {
        // 使用普通shell命令测试root权限
        let result = shell("su -c 'echo root_test'", true);

        if (result.code === 0 && result.result.includes("root_test")) {
            console.log("成功获取root权限，标记为已授权");
            root会话 = true;
            return true;
        } else {
            console.log("获取root权限失败: " + result.error);
            root会话 = false;
            return false;
        }
    } catch (e) {
        console.error("初始化Root权限出错: " + e);
        root会话 = false;
        return false;
    }
}

/**
 * 检查截图权限
 * ROOT模式下不需要检查权限，直接返回true
 * 非ROOT模式下检查系统截图权限
 */
function 检查截图权限() {
    if (当前模式 === 操作模式.ROOT) {
        console.log("ROOT模式下，无需检查截图权限");
        return true;
    }

    // 非ROOT模式不支持
    console.error("脚本已配置为仅支持ROOT模式");
    return false;
}

/**
 * 执行需要Root权限的shell命令
 * @param {string} 命令 - 要执行的shell命令
 * @returns {Object} 包含执行结果的对象
 */
function 执行Root命令(命令) {
    console.log("执行Root命令: " + 命令);

    // 检查是否已经获取root权限
    if (!root会话) {
        if (!初始化Root权限()) {
            console.log("未获取到root权限，无法执行命令");
            return {
                code: -1,
                result: "",
                error: "未获取root权限"
            };
        }
    }

    // 使用su -c执行命令
    try {
        let result = shell("su -c '" + 命令 + "'", true);

        if (result.code === 0) {
            console.log("命令执行成功");
            return {
                code: 0,
                result: result.result,
                error: ""
            };
        } else {
            console.log("命令执行失败: " + result.error);
            return {
                code: result.code,
                result: result.result,
                error: result.error
            };
        }
    } catch (e) {
        console.error("执行命令出错: " + e);
        return {
            code: -1,
            result: "",
            error: e.toString()
        };
    }
}

/**
 * 检查是否已进入小红书
 * @returns {boolean} 是否在小红书中
 */
function 检查是否进入小红书() {
    // 使用Root命令获取当前应用包名
    let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
    if (result.code === 0) {
        let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
        if (match && match[1]) {
            let 当前包名 = match[1];
            console.log("当前应用包名: " + 当前包名);
            return 当前包名 === "com.xingin.xhs";
        }
    }

    console.log("无法确认是否已进入小红书");
    return false;
}

/**
 * 获取XML文字信息
 * 通过解析XML获取匹配的文字和坐标信息，功能等同于OCR.获取屏幕文字信息
 *
 * @param {string} 必须关键词 - 必须包含的关键词
 * @param {string} 匹配关键词 - 要匹配的关键词模式，支持通配符*和|分隔
 * @param {number} 匹配数量 - 匹配的数量限制，0表示不限制
 * @param {string} 排除关键词 - 要排除的关键词
 * @param {boolean|null} 获取新XML - true/null时获取新XML，false时使用缓存
 * @returns {Array} 匹配的文字信息数组，格式与OCR保持一致
 */
function 获取XML文字信息(必须关键词 = "", 匹配关键词 = "", 匹配数量 = 0, 排除关键词 = "", 获取新XML = null) {
    console.log("🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛");
    console.log(`参数：必须关键词="${必须关键词}", 匹配关键词="${匹配关键词}", 匹配数量=${匹配数量}, 排除关键词="${排除关键词}"`);

    try {
        // 确保参数为字符串
        必须关键词 = String(必须关键词 || "");
        匹配关键词 = String(匹配关键词 || "");
        排除关键词 = String(排除关键词 || "");

        // 获取XML内容
        let xmlContent = null;
        if (获取新XML !== false) {
            console.log("获取新的XML内容");
            xmlContent = 通过shell获取窗口XML();
            if (!xmlContent) {
                console.error("❌❌❌ 获取窗口XML失败 ❌❌❌");
                return [];
            }
        } else {
            console.log("使用缓存的XML内容");
            // 这里可以实现XML缓存逻辑，暂时直接获取新的
            xmlContent = 通过shell获取窗口XML();
        }
        //console.log(xmlContent);
        console.log("开始解析XML获取所有文字...");
        let allTextWithPos = [];

        // 解析XML，提取所有文字和坐标
        // 匹配所有node标签（包括自闭合和非自闭合）
        const nodePattern = /<node[^>]*(?:\/>|>)/g;
        let nodeMatch;

        while ((nodeMatch = nodePattern.exec(xmlContent)) !== null) {
            let nodeStr = nodeMatch[0];
            console.log(`找到节点: ${nodeStr.substring(0, 100)}...`);

            // 提取text属性
            let textMatch = nodeStr.match(/text=['"]([^'"]*)['"]/);
            // 提取content-desc属性
            let contentDescMatch = nodeStr.match(/content-desc=['"]([^'"]*)['"]/);
            // 提取bounds属性（支持单引号和双引号）
            let boundsMatch = nodeStr.match(/bounds=['"]?\[(\d+),(\d+)\]\[(\d+),(\d+)\]['"]?/);

            let text = null;
            if (textMatch && textMatch[1] && textMatch[1].trim().length > 0) {
                text = textMatch[1].trim();
                console.log(`从text属性提取到文字: "${text}"`);
            } else if (contentDescMatch && contentDescMatch[1] && contentDescMatch[1].trim().length > 0) {
                text = contentDescMatch[1].trim();
                console.log(`从content-desc属性提取到文字: "${text}"`);
            }

            // 调试bounds匹配
            if (boundsMatch) {
                console.log(`找到bounds: ${boundsMatch[0]}`);
            } else {
                console.log(`未找到bounds，节点内容: ${nodeStr}`);
            }

            if (text && boundsMatch) {
                let left = parseInt(boundsMatch[1]);
                let top = parseInt(boundsMatch[2]);
                let right = parseInt(boundsMatch[3]);
                let bottom = parseInt(boundsMatch[4]);

                console.log(`添加文字项: "${text}" 坐标: [${left},${top}][${right},${bottom}]`);
                allTextWithPos.push({
                    label: text,
                    confidence: 1.0, // XML解析固定置信度为1.0
                    bounds: {
                        left: left,
                        top: top,
                        right: right,
                        bottom: bottom
                    }
                });
            } else if (text) {
                console.log(`有文字但无坐标: "${text}"`);
            } else if (boundsMatch) {
                console.log(`节点有坐标但无有效文字: text="${textMatch ? textMatch[1] : 'null'}" content-desc="${contentDescMatch ? contentDescMatch[1] : 'null'}"`);
            }
        }

        console.log(`XML解析成功，共提取到 ${allTextWithPos.length} 个文字项`);

        if (allTextWithPos.length > 0) {
            console.log(`提取到的所有文字: ${allTextWithPos.map(item => `"${item.label}"`).join(", ")}`);
            // 详细输出每个文字项的信息
            allTextWithPos.forEach((item, index) => {
                console.log(`文字项${index + 1}: "${item.label}" 坐标: [${item.bounds.left},${item.bounds.top}][${item.bounds.right},${item.bounds.bottom}]`);
            });
        } else {
            console.log("XML未提取到任何文字");
            return [];
        }

        // 过滤逻辑
        let 匹配结果 = [];

        // 处理匹配关键词模式
        let 匹配模式列表 = [];
        if (匹配关键词) {
            匹配模式列表 = 匹配关键词.split("|").map(pattern => pattern.trim());
            console.log(`匹配模式列表: [${匹配模式列表.map(p => `"${p}"`).join(", ")}]`);
        }

        // 处理排除关键词
        let 排除模式列表 = [];
        if (排除关键词) {
            排除模式列表 = 排除关键词.split("|").map(pattern => pattern.trim());
        }

        // 第一步：先检查所有文字是否包含排除关键词，如果包含直接返回空结果
        if (排除模式列表.length > 0) {
            console.log(`🔍 开始检查排除关键词: [${排除模式列表.join(", ")}]`);
            for (let item of allTextWithPos) {
                let text = item.label;
                console.log(`🔍 检查排除关键词: "${text}" vs [${排除模式列表.join(", ")}]`);

                for (let pattern of 排除模式列表) {
                    // 排除关键词支持两种模式：
                    // 1. 通配符模式：如 "*小红书*" 使用正则匹配
                    // 2. 包含模式：如 "小红书" 使用包含匹配（兼容模式=true）
                    let 启用兼容模式 = !pattern.includes("*"); // 没有通配符时启用包含匹配
                    let 匹配结果 = 匹配文字模式(text, pattern, 启用兼容模式);
                    console.log(`🔍 排除检查: "${text}" vs "${pattern}" 结果: ${匹配结果} (${pattern.includes("*") ? "通配符模式" : "包含模式"})`);
                    if (匹配结果) {
                        console.log(`❌ 发现排除关键词: "${text}" 匹配排除模式: "${pattern}"`);
                        console.log(`🚫 包含排除关键词，直接返回空结果`);
                        console.log(`🔚🔚🔚====获取XML文字信息调用结束，因排除关键词返回空结果====🔚🔚🔚`);
                        return [];
                    }
                }
            }
            console.log(`✅ 排除关键词检查通过，继续匹配流程`);
        }

        // 第二步：检查匹配条件（只有通过排除检查的才会到这里）
        for (let item of allTextWithPos) {
            let text = item.label;
            let 应该匹配 = false;
            let 匹配原因 = "";

            // 第二步：检查必须关键词（支持|分割和通配符匹配）
            let 通过必须关键词 = false;
            if (必须关键词) {
                // 处理必须关键词的|分割
                let 必须关键词列表 = 必须关键词.split("|").map(pattern => pattern.trim());
                console.log(`🔍 检查必须关键词: "${text}" vs [${必须关键词列表.join(", ")}]`);

                for (let pattern of 必须关键词列表) {
                    let 匹配结果 = false;

                    // 如果包含通配符，使用通配符匹配
                    if (pattern.includes("*")) {
                        console.log(`🔍 通配符匹配: "${text}" vs "${pattern}"`);
                        匹配结果 = 匹配文字模式(text, pattern);
                    } else {
                        // 使用字符串包含匹配
                        console.log(`🔍 包含匹配: "${text}" vs "${pattern}"`);
                        匹配结果 = text.includes(pattern);
                    }

                    console.log(`🔍 匹配结果: ${匹配结果}`);
                    if (匹配结果) {
                        通过必须关键词 = true;
                        匹配原因 = `必须关键词(${pattern})`;
                        break; // 找到一个匹配就够了
                    }
                }

                if (!通过必须关键词) {
                    continue; // 没有匹配任何必须关键词，跳过这个文字
                }
            }

            // 如果通过了必须关键词检查，直接标记为应该匹配
            if (通过必须关键词) {
                应该匹配 = true;
            }

            // 检查匹配关键词（即使已经通过必须关键词，也要检查匹配关键词）
            if (匹配模式列表.length > 0) {
                console.log(`正在检查文字 "${text}" 是否匹配模式...`);
                for (let pattern of 匹配模式列表) {
                    let 匹配结果 = 匹配文字模式(text, pattern);
                    console.log(`  模式 "${pattern}" 匹配结果: ${匹配结果}`);
                    if (匹配结果) {
                        应该匹配 = true;
                        if (匹配原因) {
                            匹配原因 += " + 匹配关键词";
                        } else {
                            匹配原因 = "匹配关键词";
                        }
                        break;
                    }
                }
            } else if (!通过必须关键词) {
                应该匹配 = true; // 没有匹配条件且没有必须关键词时默认匹配
                匹配原因 = "默认匹配";
            }

            if (应该匹配) {
                匹配结果.push(item);
                console.log(`✅ 匹配到文字: "${text}" 坐标: [${item.bounds.left},${item.bounds.top}][${item.bounds.right},${item.bounds.bottom}] 原因: ${匹配原因}`);

                // 检查数量限制
                if (匹配数量 > 0 && 匹配结果.length >= 匹配数量) {
                    break;
                }
            }
        }

        console.log(`🔚🔚🔚====获取XML文字信息调用结束，匹配到 ${匹配结果.length} 个结果====🔚🔚🔚`);
        return 匹配结果;

    } catch (e) {
        console.error("❌❌❌ XML文字信息获取出错: " + e.message + " ❌❌❌");
        console.log("🔚🔚🔚====获取XML文字信息调用结束====🔚🔚🔚");
        return [];
    }
}

/**
 * 获取XML文字信息
 * 通过解析XML获取匹配的文字和坐标信息，功能等同于OCR.获取屏幕文字信息
 *
 * @param {string} 必须关键词 - 必须包含的关键词
 * @param {string} 匹配关键词 - 要匹配的关键词模式，支持通配符*和|分隔
 * @param {number} 匹配数量 - 匹配的数量限制，0表示不限制
 * @param {string} 排除关键词 - 要排除的关键词
 * @param {boolean|null} 获取新XML - true/null时获取新XML，false时使用缓存
 * @returns {Array} 匹配的文字信息数组，格式与OCR保持一致
 */


// 辅助函数：解析XML获取兴趣选项
function 小红书_解析XML获取兴趣选项(xmlContent) {
    var 屏幕高度 = typeof device !== 'undefined' ? device.height : 2400;
    var Y坐标下限 = Math.floor(屏幕高度 * 0.1);  // 排除顶部10%
    var Y坐标上限 = Math.floor(屏幕高度 * 0.9);  // 排除底部10%（Y坐标大于90%）
    var 兴趣选项 = [];

    console.log(`📱 屏幕高度: ${屏幕高度}, Y坐标范围: ${Y坐标下限} - ${Y坐标上限} (排除顶部10%和底部10%区域)`);

    const nodePattern = /<node[^>]*(?:\/>|>)/g;
    let nodeMatch;

    while ((nodeMatch = nodePattern.exec(xmlContent)) !== null) {
        let nodeStr = nodeMatch[0];
        let textMatch = nodeStr.match(/text=['"]([^'"]*)['"]/);
        let boundsMatch = nodeStr.match(/bounds=['"]?\[(\d+),(\d+)\]\[(\d+),(\d+)\]['"]?/);

        if (textMatch && textMatch[1] && textMatch[1].trim().length > 0 && boundsMatch) {
            let text = textMatch[1].trim();
            let top = parseInt(boundsMatch[2]);

            // Y坐标过滤：排除顶部10%和底部10%区域
            if (top >= Y坐标下限 && top <= Y坐标上限) {
                兴趣选项.push({
                    label: text,
                    bounds: {
                        left: parseInt(boundsMatch[1]),
                        top: top,
                        right: parseInt(boundsMatch[3]),
                        bottom: parseInt(boundsMatch[4])
                    }
                });
            } else if (top > Y坐标上限) {
                console.log(`⚠️ 跳过底部区域元素: "${text}" Y坐标: ${top} (超过90%)`);
            }
        }
    }

    console.log(`📋 XML解析完成，找到 ${兴趣选项.length} 个有效区域的文字元素（排除顶部10%和底部10%）`);
    return 兴趣选项;
}

// 辅助函数：过滤兴趣选项
function 小红书_过滤兴趣选项(兴趣选项, 已点击索引) {
    var 排除列表 = ["再选1个兴趣", "再选2个兴趣", "再选3个兴趣", "选择4个兴趣", "跳过", "选择兴趣推荐更精准", "已有账号，去登录", "确定", "摄影", "兴趣爱好", "影视娱乐", "运动"];
    var 可选择的选项 = [];

    for (var i = 0; i < 兴趣选项.length; i++) {
        var 选项 = 兴趣选项[i];
        var text = 选项.label;
        var 应该排除 = false;

        for (var j = 0; j < 排除列表.length; j++) {
            if (text.includes(排除列表[j])) {
                应该排除 = true;
                break;
            }
        }

        var 坐标标识 = `${选项.bounds.left}-${选项.bounds.top}`;
        var 已点击过 = 已点击索引.includes(坐标标识);

        if (!应该排除 && !已点击过) {
            可选择的选项.push(选项);
        }
    }
    return 可选择的选项;
}

// 辅助函数：在XML中查找确定类按钮（不过滤坐标）
function 小红书_查找确定类按钮(xmlContent) {
    if (!xmlContent) return null;

    // 要查找的确定类按钮关键词
    var 确定类关键词 = ["再选1个兴趣", "再选2个兴趣", "再选3个兴趣", "选择4个兴趣", "确定"];

    console.log(`🔍 在XML中查找确定类按钮: [${确定类关键词.join(", ")}]`);

    const nodePattern = /<node[^>]*(?:\/>|>)/g;
    let nodeMatch;

    while ((nodeMatch = nodePattern.exec(xmlContent)) !== null) {
        let nodeStr = nodeMatch[0];
        let textMatch = nodeStr.match(/text=['"]([^'"]*)['"]/);
        let boundsMatch = nodeStr.match(/bounds=['"]?\[(\d+),(\d+)\]\[(\d+),(\d+)\]['"]?/);

        if (textMatch && textMatch[1] && textMatch[1].trim().length > 0 && boundsMatch) {
            let text = textMatch[1].trim();

            // 检查是否匹配确定类关键词
            for (var i = 0; i < 确定类关键词.length; i++) {
                var 关键词 = 确定类关键词[i];
                if (text === 关键词 || text.includes(关键词)) {
                    var left = parseInt(boundsMatch[1]);
                    var top = parseInt(boundsMatch[2]);
                    var right = parseInt(boundsMatch[3]);
                    var bottom = parseInt(boundsMatch[4]);

                    // 检查坐标是否有效（不为0）
                    if (left === 0 || top === 0 || right === 0 || bottom === 0) {
                        console.log(`⚠️ 找到确定类按钮但坐标无效: "${text}" (${left},${top},${right},${bottom}) - 使用备用坐标`);

                        // 获取虚拟按键信息计算备用坐标
                        let 虚拟按键信息 = 获取虚拟按键坐标信息();
                        var 屏幕宽度 = typeof device !== 'undefined' ? device.width : 1440;
                        var 屏幕高度 = typeof device !== 'undefined' ? device.height : 3200;
                        var 虚拟按键高度 = 虚拟按键信息 ? 虚拟按键信息.height : 0;

                        // 计算备用坐标：屏幕高度 - 虚拟按键高度 - 60
                        var 备用X = Math.floor(屏幕宽度 * 0.5);
                        var 备用Y = 屏幕高度 - 虚拟按键高度 - 60;

                        // 添加随机偏移 ±5像素
                        var 随机偏移X = Math.floor(Math.random() * 11) - 5; // -5 到 +5
                        var 随机偏移Y = Math.floor(Math.random() * 11) - 5; // -5 到 +5

                        var 最终X = 备用X + 随机偏移X;
                        var 最终Y = 备用Y + 随机偏移Y;

                        console.log(`📱 屏幕: ${屏幕宽度}x${屏幕高度}, 虚拟按键高度: ${虚拟按键高度}`);
                        console.log(`🧮 计算过程: ${屏幕高度} - ${虚拟按键高度} - 60 = ${备用Y}px`);
                        console.log(`🎯 备用坐标: (${备用X}, ${备用Y}) + 随机偏移(${随机偏移X}, ${随机偏移Y}) = (${最终X}, ${最终Y})`);

                        return {
                            label: text,
                            bounds: {
                                left: 最终X - 50,
                                top: 最终Y - 25,
                                right: 最终X + 50,
                                bottom: 最终Y + 25
                            },
                            isBackupCoordinate: true
                        };
                    }

                    console.log(`✅ 找到有效的确定类按钮: "${text}" (匹配关键词: ${关键词}) 坐标: [${left},${top}][${right},${bottom}]`);
                    return {
                        label: text,
                        bounds: {
                            left: left,
                            top: top,
                            right: right,
                            bottom: bottom
                        },
                        isBackupCoordinate: false
                    };
                }
            }
        }
    }

    console.log("❌ 未找到任何确定类按钮");
    return null;
}

function 小红书兴趣选择() {
    console.log("🎯 开始小红书兴趣选择...");

    try {
        var 已选择兴趣 = [];
        var 已点击索引 = [];
        var 记住的确定按钮位置 = null; // 记住确定类按钮的位置

        // === 第一阶段：首次获取XML并选择4个兴趣 ===
        console.log("\n=== 第一阶段：首次选择4个兴趣 ===");
        var xmlContent = 通过shell获取窗口XML();
        if (!xmlContent) {
            console.log("❌ 获取XML失败");
            return false;
        }

        if (!xmlContent.includes("选择兴趣推荐更精准")) {
            return false;
        }

        // console.log(`📋 页面检查: 已有账号去登录=${包含已有账号去登录}, 包含兴趣=${包含兴趣关键词}`);
        // if (!包含已有账号去登录 && !包含兴趣关键词) {
        //     console.log("⚠️ 当前页面可能不是兴趣选择页面，没有找到'已有账号，去登录'或'兴趣'关键词");
        //     console.log("🔄 可能需要重新导航到兴趣选择页面");
        //     return false;
        // } else {
        //     console.log("✅ 确认在兴趣选择页面");
        // }

        var 兴趣选项 = 小红书_解析XML获取兴趣选项(xmlContent);
        var 可选择的选项 = 小红书_过滤兴趣选项(兴趣选项, 已点击索引);

        if (可选择的选项.length === 0) {
            console.log("❌ 未找到可选择的兴趣选项");
            return false;
        }

        var 首次选择数量 = Math.min(4, 可选择的选项.length);
        console.log(`🎯 准备选择 ${首次选择数量} 个兴趣`);

        for (var i = 0; i < 首次选择数量; i++) {
            var 随机索引 = Math.floor(Math.random() * 可选择的选项.length);
            var 选中的选项 = 可选择的选项[随机索引];

            console.log(`👆 点击兴趣: ${选中的选项.label}`);
            var x = Math.floor((选中的选项.bounds.left + 选中的选项.bounds.right) / 2);
            var y = Math.floor((选中的选项.bounds.top + 选中的选项.bounds.bottom) / 2);
            点击(x, y);

            已选择兴趣.push(选中的选项.label);
            var 坐标标识 = `${选中的选项.bounds.left}-${选中的选项.bounds.top}`;
            已点击索引.push(坐标标识);
            可选择的选项.splice(随机索引, 1);

            sleep(800);
        }

        console.log(`✅ 首次选择完成，已选择 ${已选择兴趣.length} 个兴趣: ${已选择兴趣.join(", ")}`);
        sleep(1500);

        // 在当前XML中查找确定或继续选择按钮（不重新获取XML）
        var 确定类按钮 = 小红书_查找确定类按钮(xmlContent);
        if (确定类按钮) {
            console.log(`✅ 在当前XML中找到按钮: ${确定类按钮.label}`);
            var x = Math.floor((确定类按钮.bounds.left + 确定类按钮.bounds.right) / 2);
            var y = Math.floor((确定类按钮.bounds.top + 确定类按钮.bounds.bottom) / 2);

            if (确定类按钮.isBackupCoordinate) {
                console.log(`🎯 使用备用坐标点击: (${x}, ${y})`);
            } else {
                console.log(`📍 使用XML坐标点击: (${x}, ${y})`);
            }

            // 记住这个按钮的位置
            记住的确定按钮位置 = { x: x, y: y, label: 确定类按钮.label, isBackup: 确定类按钮.isBackupCoordinate };
            console.log(`� 记住确定按钮位置: (${x}, ${y}) - ${确定类按钮.label} ${确定类按钮.isBackupCoordinate ? '(备用坐标)' : '(XML坐标)'}`);

            console.log(`�👆 点击按钮 (${x}, ${y})`);
            点击(x, y);
            sleep(2000);
            console.log("✅ 按钮点击完成，兴趣选择流程结束");
            return true;
        } else {
            console.log("⚠️ 在当前XML中未找到确定类按钮，进入循环选择模式");

        }

        // === 第二阶段：循环模式，每次获取XML选择1个兴趣 ===
        console.log("\n=== 第二阶段：循环选择模式（每次1个） ===");
        var 最大尝试次数 = 15;
        var 当前尝试次数 = 0;

        while (当前尝试次数 < 最大尝试次数) {
            当前尝试次数++;
            console.log(`\n--- 第 ${当前尝试次数} 次追加选择 ---`);

            // 重新获取XML
            xmlContent = 通过shell获取窗口XML();
            if (!xmlContent) {
                console.log("❌ 获取XML失败");
                sleep(1000);
                continue;
            }
            if (!xmlContent.includes("选择兴趣推荐更精准")) { //不存在就是上面点击成功了
                console.log("⚠️ 当前页面可能不是兴趣选择页面，没有找到'选择兴趣推荐更精准'关键词");
                return true;
            }
            // 如果有记住的确定按钮位置，直接点击
            if (记住的确定按钮位置) {
                var 位置类型 = 记住的确定按钮位置.isBackup ? '(备用坐标)' : '(XML坐标)';
                console.log(`📍 使用记住的确定按钮位置: (${记住的确定按钮位置.x}, ${记住的确定按钮位置.y}) - ${记住的确定按钮位置.label} ${位置类型}`);
                console.log(`👆 点击记住的确定按钮位置`);
                点击(记住的确定按钮位置.x, 记住的确定按钮位置.y);
                sleep(2000);
                //console.log("✅ 确定按钮点击完成，兴趣选择流程结束");
                continue
                //return true;
            } else {
                // 如果没有记住的位置，重新查找
                var 确定类按钮 = 小红书_查找确定类按钮(xmlContent);
                if (确定类按钮) {
                    console.log(`✅ 重新找到确定类按钮: ${确定类按钮.label}`);
                    var x = Math.floor((确定类按钮.bounds.left + 确定类按钮.bounds.right) / 2);
                    var y = Math.floor((确定类按钮.bounds.top + 确定类按钮.bounds.bottom) / 2);

                    if (确定类按钮.isBackupCoordinate) {
                        console.log(`🎯 重新查找使用备用坐标: (${x}, ${y})`);
                    } else {
                        console.log(`📍 重新查找使用XML坐标: (${x}, ${y})`);
                    }

                    // 记住这个位置
                    记住的确定按钮位置 = { x: x, y: y, label: 确定类按钮.label, isBackup: 确定类按钮.isBackupCoordinate };
                    console.log(`📍 记住确定按钮位置: (${x}, ${y}) - ${确定类按钮.label} ${确定类按钮.isBackupCoordinate ? '(备用坐标)' : '(XML坐标)'}`);

                    console.log(`👆 点击确定类按钮 (${x}, ${y})`);
                    点击(x, y);
                    sleep(2000);
                    //console.log("✅ 确定类按钮点击完成，兴趣选择流程结束");
                    continue;
                    //return true;
                }
            }

            // 判断是否还在兴趣选择页面
            包含已有账号去登录 = xmlContent.includes("已有账号，去登录");
            包含兴趣关键词 = xmlContent.includes("兴趣");

            console.log(`📋 循环中页面检查: 已有账号去登录=${包含已有账号去登录}, 包含兴趣=${包含兴趣关键词}`);

            if (!包含已有账号去登录 && !包含兴趣关键词) {
                console.log("⚠️ 页面已跳转，不再是兴趣选择页面");
                console.log("🎉 可能兴趣选择已完成，页面自动跳转了");
                return true; // 认为选择成功
            }

            兴趣选项 = 小红书_解析XML获取兴趣选项(xmlContent);
            可选择的选项 = 小红书_过滤兴趣选项(兴趣选项, 已点击索引);

            console.log(`📋 找到 ${可选择的选项.length} 个可选择的兴趣选项`);

            if (可选择的选项.length === 0) {
                console.log("⚠️ 没有更多可选择的兴趣选项");
                sleep(1000);
                continue;
            }

            // 在循环中每次只选择1个兴趣
            var 随机索引 = Math.floor(Math.random() * 可选择的选项.length);
            var 选中的选项 = 可选择的选项[随机索引];

            console.log(`👆 点击兴趣: ${选中的选项.label}`);

            // 计算点击坐标（中心点）
            var x = Math.floor((选中的选项.bounds.left + 选中的选项.bounds.right) / 2);
            var y = Math.floor((选中的选项.bounds.top + 选中的选项.bounds.bottom) / 2);

            // 点击
            点击(x, y);

            // 记录已选择的兴趣和坐标
            已选择兴趣.push(选中的选项.label);
            var 坐标标识 = `${选中的选项.bounds.left}-${选中的选项.bounds.top}`;
            已点击索引.push(坐标标识);

            sleep(800);

            console.log(`✅ 当前已选择 ${已选择兴趣.length} 个兴趣: ${已选择兴趣.join(", ")}`);

            // 选择兴趣后，如果有记住的确定按钮位置，直接点击
            if (记住的确定按钮位置) {
                console.log(`📍 选择兴趣后，使用记住的确定按钮位置: (${记住的确定按钮位置.x}, ${记住的确定按钮位置.y})`);
                console.log(`👆 点击记住的确定按钮位置`);
                点击(记住的确定按钮位置.x, 记住的确定按钮位置.y);
                sleep(2000);
                console.log("✅ 确定按钮点击完成，兴趣选择流程结束");
                return true;
            }

            // 等待页面更新
            sleep(1500);
        }

        console.log("❌ 达到最大尝试次数，兴趣选择可能失败");
        return false;

    } catch (e) {
        console.error("❌ 小红书兴趣选择出错:", e.message);
        return false;
    }
}




// 精确提取虚拟按键坐标信息
function 获取虚拟按键坐标信息() {
    console.log("🔍 开始提取虚拟按键坐标信息...");

    try {
        let result = shell("dumpsys SurfaceFlinger | grep -A 10 -B 5 'NavigationBar0'", true);

        if (result && result.result) {
            let output = result.result;
            console.log("原始SurfaceFlinger数据片段:");
            console.log(output.substring(0, 800) + "...");

            // 方法1：提取 pos 和 size 信息
            let posMatch = output.match(/pos=\((\d+),(\d+)\)/);
            let sizeMatch = output.match(/size=\((\d+),\s*(\d+)\)/);

            if (posMatch && sizeMatch) {
                let startX = parseInt(posMatch[1]);
                let startY = parseInt(posMatch[2]);
                let width = parseInt(sizeMatch[1]);
                let height = parseInt(sizeMatch[2]);

                let endX = startX + width;
                let endY = startY + height;

                console.log(`✅ 方法1 - pos和size提取:`);
                console.log(`  起始坐标: (${startX}, ${startY})`);
                console.log(`  尺寸: ${width} x ${height}`);
                console.log(`  结束坐标: (${endX}, ${endY})`);

                return {
                    method: "pos_size",
                    startX: startX,
                    startY: startY,
                    endX: endX,
                    endY: endY,
                    width: width,
                    height: height,
                    bounds: `[${startX},${startY}][${endX},${endY}]`
                };
            }

            // 方法2：提取可见区域信息
            let regionMatch = output.match(/\[\s*(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]/);
            if (regionMatch) {
                let left = parseInt(regionMatch[1]);
                let top = parseInt(regionMatch[2]);
                let right = parseInt(regionMatch[3]);
                let bottom = parseInt(regionMatch[4]);

                let width = right - left;
                let height = bottom - top;

                console.log(`✅ 方法2 - 可见区域提取:`);
                console.log(`  区域边界: [${left}, ${top}, ${right}, ${bottom}]`);
                console.log(`  尺寸: ${width} x ${height}`);

                return {
                    method: "visible_region",
                    startX: left,
                    startY: top,
                    endX: right,
                    endY: bottom,
                    width: width,
                    height: height,
                    bounds: `[${left},${top}][${right},${bottom}]`
                };
            }

        }
    } catch (e) {
        console.log("❌ SurfaceFlinger提取失败:", e.message);
    }

    // 备用方法：从dumpsys window提取
    try {
        console.log("\n📱 备用方法：dumpsys window");
        let result2 = shell("dumpsys window windows | grep -A 20 'NavigationBar0'", true);

        if (result2 && result2.result) {
            let output2 = result2.result;

            // 查找 mFrame=[left,top][right,bottom]
            let frameMatch = output2.match(/mFrame=\[(\d+),(\d+)\]\[(\d+),(\d+)\]/);
            if (frameMatch) {
                let left = parseInt(frameMatch[1]);
                let top = parseInt(frameMatch[2]);
                let right = parseInt(frameMatch[3]);
                let bottom = parseInt(frameMatch[4]);

                let width = right - left;
                let height = bottom - top;

                console.log(`✅ 备用方法 - mFrame提取:`);
                console.log(`  Frame边界: [${left}, ${top}][${right}, ${bottom}]`);
                console.log(`  尺寸: ${width} x ${height}`);

                return {
                    method: "mFrame",
                    startX: left,
                    startY: top,
                    endX: right,
                    endY: bottom,
                    width: width,
                    height: height,
                    bounds: `[${left},${top}][${right},${bottom}]`
                };
            }
        }
    } catch (e) {
        console.log("❌ dumpsys window提取失败:", e.message);
    }

    // 如果所有方法都失败，返回null
    console.log("❌ 无法提取虚拟按键坐标信息");
    return null;
}



/**
 * 匹配文字模式
 * 支持三种匹配模式：
 * 1. 有通配符(*): 使用模糊匹配，如 "*同意*" 可以匹配 "不同意"
 * 2. 无通配符: 使用精确匹配，如 "同意" 只能匹配 "同意"
 * 3. 兼容模式: 如果精确匹配失败，回退到包含匹配（保持向后兼容）
 *
 * @param {string} text - 要匹配的文字
 * @param {string} pattern - 匹配模式，支持*通配符
 * @param {boolean} 兼容模式 - 是否启用兼容模式（默认false）
 * @returns {boolean} 是否匹配
 */
function 匹配文字模式(text, pattern, 兼容模式 = false) {
    if (!pattern) return true;

    // 处理通配符*
    if (pattern.includes("*")) {
        // 先转义其他特殊字符，但保留*不转义
        let regexPattern = pattern.replace(/[.+?^${}()|[\]\\]/g, "\\$&");
        // 然后将*替换为正则表达式的.*
        regexPattern = regexPattern.replace(/\*/g, ".*");
        console.log(`🔍 通配符匹配详情: 文字="${text}", 模式="${pattern}", 正则="${regexPattern}"`);
        try {
            let regex = new RegExp(regexPattern, "i"); // 不区分大小写
            let 匹配结果 = regex.test(text);
            console.log(`🔍 正则匹配结果: ${匹配结果}`);
            return 匹配结果;
        } catch (e) {
            console.error("正则表达式错误:", e.message);
            return text.toLowerCase().includes(pattern.toLowerCase().replace(/\*/g, ""));
        }
    } else {
        // 普通字符串匹配
        let 精确匹配结果 = text.toLowerCase() === pattern.toLowerCase();

        // 如果精确匹配成功，直接返回
        if (精确匹配结果) {
            return true;
        }

        // 如果启用兼容模式且精确匹配失败，尝试包含匹配
        if (兼容模式) {
            let 包含匹配结果 = text.toLowerCase().includes(pattern.toLowerCase());
            if (包含匹配结果) {
                console.log(`⚠️ 兼容模式匹配: "${text}" 包含 "${pattern}"`);
                return true;
            }
        }

        return false;
    }
}

/**
 * 点击XML关键词
 * 根据XML解析结果点击指定关键词，功能等同于OCR.点击指定关键词
 *
 * @param {Array} XML结果 - 从获取XML文字信息返回的结果数组
 * @param {string} 匹配关键词 - 要点击的关键词模式，支持通配符*和|分隔
 * @param {string} 排除文字 - 要排除的关键词，默认为空
 * @param {number} 偏移X - X轴偏移量，默认为0
 * @param {number} 偏移Y - Y轴偏移量，默认为0
 * @param {number} 点击次数 - 点击次数，默认为1
 * @param {number} 点击间隔 - 点击间隔(毫秒)，默认为50
 * @returns {boolean} 是否成功点击
 */
function 点击XML关键词(XML结果, 匹配关键词, 排除文字 = "", 偏移X = 0, 偏移Y = 0, 点击次数 = 1, 点击间隔 = 50) {
    console.log("🔛🔛🔛====点击XML关键词开始====🔛🔛🔛");
    console.log(`要点击的关键词模式: "${匹配关键词}"`);

    try {
        if (!XML结果 || !Array.isArray(XML结果) || XML结果.length === 0) {
            console.log("❌ XML结果为空，无法点击");
            return false;
        }

        // 处理匹配关键词模式
        let 匹配模式列表 = [];
        if (匹配关键词) {
            匹配模式列表 = 匹配关键词.split("|").map(pattern => pattern.trim());
        } else {
            console.log("❌ 匹配关键词为空");
            return false;
        }

        // 处理排除文字模式
        let 排除模式列表 = [];
        if (排除文字) {
            排除模式列表 = 排除文字.split("|").map(pattern => pattern.trim());
            console.log(`排除文字模式: [${排除模式列表.map(p => `"${p}"`).join(", ")}]`);
        }

        // 查找匹配的文字项
        let 目标项 = null;
        for (let item of XML结果) {
            let text = item.label;
            let 应该排除 = false;

            // 检查是否应该排除
            if (排除模式列表.length > 0) {
                console.log(`🔍 检查排除关键词: "${text}" vs [${排除模式列表.join(", ")}]`);
                for (let excludePattern of 排除模式列表) {
                    // 排除关键词支持两种模式：
                    // 1. 通配符模式：如 "*小红书*" 使用正则匹配
                    // 2. 包含模式：如 "小红书" 使用包含匹配（兼容模式=true）
                    let 启用兼容模式 = !excludePattern.includes("*"); // 没有通配符时启用包含匹配
                    let 匹配结果 = 匹配文字模式(text, excludePattern, 启用兼容模式);
                    console.log(`🔍 排除检查: "${text}" vs "${excludePattern}" 结果: ${匹配结果} (${excludePattern.includes("*") ? "通配符模式" : "包含模式"})`);
                    if (匹配结果) {
                        应该排除 = true;
                        console.log(`❌ 排除文字: "${text}" 匹配排除模式: "${excludePattern}"`);
                        break;
                    }
                }
            }

            // 如果被排除，跳过此项
            if (应该排除) {
                continue;
            }

            // 检查是否匹配目标关键词
            for (let pattern of 匹配模式列表) {
                // 根据是否有通配符决定匹配模式：
                // 有通配符(*) - 使用模糊匹配
                // 无通配符 - 使用精确匹配（兼容模式=false）
                let 启用兼容模式 = pattern.includes("*");
                if (匹配文字模式(text, pattern, 启用兼容模式)) {
                    目标项 = item;
                    console.log(`✅ 找到匹配项: "${text}" 匹配模式: "${pattern}" 匹配类型: ${启用兼容模式 ? '模糊匹配' : '精确匹配'}`);
                    break;
                }
            }
            if (目标项) break;
        }

        if (!目标项) {
            console.log("❌ 未找到匹配的关键词");
            return false;
        }

        // 计算点击坐标（中心点 + 偏移）
        let centerX = (目标项.bounds.left + 目标项.bounds.right) / 2 + 偏移X;
        let centerY = (目标项.bounds.top + 目标项.bounds.bottom) / 2 + 偏移Y;

        console.log(`🎯 准备点击: "${目标项.label}" 坐标: (${Math.round(centerX)}, ${Math.round(centerY)}) 偏移: (${偏移X}, ${偏移Y}) 次数: ${点击次数} 间隔: ${点击间隔}ms`);

        // 执行多次点击
        for (let i = 0; i < 点击次数; i++) {
            if (i > 0) {
                console.log(`第${i + 1}次点击，间隔${点击间隔}ms`);
                sleep(点击间隔);
            }
            点击(centerX, centerY);
        }

        console.log("✅ 点击执行完成");
        console.log("🔚🔚🔚====点击XML关键词结束====🔚🔚🔚");
        return true;

    } catch (e) {
        console.error("❌❌❌ 点击XML关键词出错: " + e.message + " ❌❌❌");
        console.log("🔚🔚🔚====点击XML关键词结束====🔚🔚🔚");
        return false;
    }
}

// 导出所有函数
module.exports = {
    操作模式,
    设置交互操作模式,
    获取交互操作模式,
    设置窗口信息模式,
    获取窗口信息模式,
    获取当前模式,
    点击,
    连续点击,
    长按,
    滑动,
    输入文本,
    返回,
    主页键,
    sleep,
    获取窗口XML,
    通过shell获取窗口XML,
    获取元素属性,
    查找元素,
    检查是否进入小红书,
    获取XML文字信息,
    点击XML关键词,
    小红书兴趣选择,

    // 新增滑动功能
    shell滑动,
    刷视频滑动,

    // 配置管理功能
    读取本地配置,
    保存到本地配置
};